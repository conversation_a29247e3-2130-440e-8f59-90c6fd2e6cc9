import React from "react";
import { View, Text, Platform } from "react-native";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Che<PERSON> } from "lucide-react-native";
// import RNHTMLtoPDF from "react-native-html-to-pdf";
import * as Print from "expo-print";
import * as Sharing from "expo-sharing";
import * as FileSystem from "expo-file-system";
import MyProgressIcon from "@assets/svg/MyProgressIcon";
import ProgressCircle from "@assets/svg/ProgressCircle";
import ActionPlanIcon from "@assets/svg/ActionPlanIcon";
import ActivityTrends from "@assets/svg/ActivityTrends";
import CalenderMarkIcon from "@assets/svg/CalenderMarkIcon";
import Svg, { Path, SvgProps } from "react-native-svg";
import Toast from "react-native-toast-message";
import usePersistedUser from "@hooks/persistUser";
import Pressable from "@components/common/Pressable";
import { endOfDay, format, startOfDay, startOfWeek, subDays } from "date-fns";
import { trpc } from "@providers/RootProvider";
import MyRabblesLogoHeader from "@assets/svg/MyRabblesLogoHeader";

function NumberIcon(props: SvgProps) {
  return (
    <Svg
      //   xmlns="http://www.w3.org/2000/svg"
      width={17}
      height={22}
      viewBox="0 0 17 22"
      fill="none"
      {...props}
    >
      <Path
        d="M8.28 22a16.25 16.25 0 01-4.565-.65c-1.47-.455-2.708-1.075-3.715-1.86l1.731-3.13c.798.64 1.763 1.157 2.897 1.55 1.154.392 2.35.589 3.589.589 1.448 0 2.581-.3 3.4-.899.84-.6 1.259-1.405 1.259-2.417 0-.682-.178-1.28-.535-1.797-.336-.516-.945-.909-1.826-1.177-.86-.27-2.046-.403-3.558-.403h-5.32L2.77 0h12.94v3.377H4.28l2.141-1.92-.85 8.83-2.14-1.89H7.87c2.183 0 3.936.29 5.258.868 1.343.558 2.319 1.342 2.928 2.355.63.991.944 2.127.944 3.408a6.64 6.64 0 01-.944 3.47c-.63 1.054-1.596 1.901-2.897 2.541-1.28.64-2.907.961-4.88.961z"
        fill="#0B79D3"
      />
    </Svg>
  );
}

const headerLogo = `<svg xmlns="http://www.w3.org/2000/svg" width="174" height="41" viewBox="0 0 174 41" fill="none">
  <g clip-path="url(#clip0_8379_20025)">
    <mask id="mask0_8379_20025" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="23" y="0" width="143" height="41">
      <path d="M165.211 0.259766H23V40.1786H165.211V0.259766Z" fill="white"/>
    </mask>
    <g mask="url(#mask0_8379_20025)">
      <path d="M54.7211 17.8374C54.4599 17.8374 54.1513 18.028 53.7951 18.29C52.9166 18.9807 51.9668 19.8619 50.8509 20.9338C50.8509 20.9338 49.735 14.7173 48.5953 14.7173C46.672 14.7173 47.5268 21.4816 45.5799 21.4816C44.5351 21.1958 45.3186 17.6469 43.6804 17.6469C43.3005 17.6469 43.1106 17.9327 43.0156 18.3138C42.3983 18.7663 41.5909 19.4332 40.3088 19.4332C38.3381 19.4332 37.3646 16.0272 36.1063 16.0272C33.8506 16.0272 33.1146 19.0045 31.9036 19.0045C30.8114 19.0045 30.574 15.0745 28.627 15.0745C26.2052 15.0745 26.0627 20.124 25.2792 20.124C25.0418 20.124 24.9705 19.9811 24.9705 19.7428C24.9705 19.5285 25.0418 19.2188 25.0418 18.9093C25.0418 18.5997 24.9705 18.29 24.7568 18.028C24.5194 17.766 24.3057 17.6707 24.1158 17.6707C23.4035 17.6707 22.9761 19.0045 22.9761 19.5762C22.9761 21.1482 23.6409 22.7201 25.3742 22.7201C27.1549 22.7201 27.4636 17.2658 28.4608 17.2658C28.8645 17.2658 29.458 21.1005 31.6899 21.1005C33.5894 21.1005 34.3967 18.028 35.5601 18.028C36.6049 18.028 37.0085 21.0767 40.1189 21.0767C41.2111 21.0767 42.232 20.7195 42.9681 20.2193C43.3005 21.8865 44.4402 23.4585 45.4611 23.4585C48.2154 23.4585 48.2866 18.1947 48.2866 18.1947L49.1651 22.2914C49.1651 22.2914 44.084 26.8406 44.084 35.7486C44.084 38.0827 45.2474 40.1786 47.4319 40.1786C50.8509 40.1786 52.0619 33.1285 52.0619 29.675C52.0619 26.1737 51.2545 22.6248 51.2545 22.6248C52.0619 21.696 52.8454 20.8861 54.1275 20.0287C54.8636 19.5285 55.1485 18.7187 55.1485 18.3376C55.196 18.1709 55.0535 17.8374 54.7211 17.8374ZM47.2894 38.035C46.4346 38.035 45.6986 37.2967 45.6986 35.7248C45.6986 28.2935 49.6637 24.1015 49.6637 24.1015C49.6637 24.1015 50.3998 27.5314 50.3998 30.0084C50.3998 32.414 49.6875 38.035 47.2894 38.035Z" fill="#FF8E1C"/>
      <path d="M67.8275 8.85778C72.22 8.85778 74.8318 11.1443 74.8318 15.0028C74.8318 18.0516 72.9798 20.0046 69.9881 20.9811C69.5845 21.1241 69.5607 21.3146 69.9644 21.5051C71.5789 22.2912 72.6474 23.8631 73.3597 25.578L75.7578 31.2467C75.8528 31.4849 75.7578 31.6039 75.5678 31.6039H73.8583C73.6921 31.6039 73.5971 31.5325 73.5259 31.3658L71.389 26.4116C70.1069 23.2915 68.1598 21.8862 64.8833 21.8862H61.0843V31.3181C61.0843 31.5086 60.9893 31.6277 60.7757 31.6277H59.1849C58.9949 31.6277 58.8762 31.5324 58.8762 31.3181V9.14363C58.8762 8.95306 58.9712 8.83398 59.1849 8.83398H67.8275V8.85778ZM65.8093 19.9093C70.1306 19.9093 72.6 18.1468 72.6 15.1219C72.6 12.4305 70.7954 10.8585 67.8275 10.8585H61.0606V19.9331H65.8093V19.9093Z" fill="#004987"/>
      <path d="M82.2398 15.2651C80.9339 15.2651 79.723 15.4318 78.6545 15.7653L77.4198 16.1226C77.2774 16.1702 77.1824 16.2417 77.1112 16.3607C77.0637 16.4798 77.0637 16.5989 77.0875 16.7419L77.4436 18.0518C77.4911 18.1947 77.5624 18.29 77.681 18.3615C77.7998 18.4091 77.9184 18.4091 78.0609 18.3615L79.2244 18.028C80.0316 17.766 81.0289 17.6469 82.1686 17.6469C84.899 17.6469 86.4186 19.1474 86.4186 21.9103V22.1723H81.6462C77.8947 22.1961 75.6628 24.1016 75.6628 27.2217C75.6628 30.318 77.7047 32.1759 81.1238 32.1759C83.5694 32.1759 85.3264 31.4613 86.4898 29.9608C86.6798 29.7464 86.7748 29.7464 86.7748 29.7464C86.7748 29.7464 86.8223 29.7702 86.846 29.8893L86.9409 31.3898C86.9884 31.6757 87.1784 31.8424 87.4395 31.8424H88.4606C88.7692 31.8424 88.9354 31.6757 88.9354 31.366V21.8627C88.9592 17.7899 86.3712 15.2651 82.2398 15.2651ZM81.2901 29.8179C79.2956 29.8179 78.1796 28.8175 78.1796 27.055C78.1796 25.3163 79.4143 24.3635 81.6699 24.3635H86.4661V24.6256C86.4423 27.9125 84.5666 29.8179 81.2901 29.8179Z" fill="#004987"/>
      <path d="M99.2879 15.0745C97.4122 15.0745 95.9163 15.7414 94.8242 17.0038C94.6817 17.1467 94.5867 17.1467 94.5629 17.1229C94.5392 17.1229 94.5155 17.0276 94.5867 16.8609C95.0378 15.5985 95.1328 14.5981 95.1328 13.0976V8.57222C95.1328 8.23877 94.9191 8.02441 94.5867 8.02441H91.6426C91.3101 8.02441 91.0964 8.23877 91.0964 8.57222V29.5558C91.0964 29.8178 91.2389 30.0084 91.5 30.1512L94.3968 31.3422C95.6789 31.89 97.0797 32.1758 98.5994 32.1758C103.419 32.1758 106.387 28.889 106.387 23.6013C106.411 18.3375 103.68 15.0745 99.2879 15.0745ZM98.7656 18.7425C101.045 18.7425 102.422 20.5526 102.422 23.6013C102.422 26.6501 100.997 28.484 98.5994 28.484C97.6971 28.484 96.7236 28.2697 95.9638 27.9362L95.0854 27.5313V23.4823C95.0854 20.4573 96.4387 18.7425 98.7656 18.7425Z" fill="#004987"/>
      <path d="M116.549 14.884C114.697 14.884 113.202 15.5033 112.062 16.7417C112.465 15.5271 112.537 14.5505 112.537 13.0976V8.73893C112.537 8.31023 112.251 8.02441 111.825 8.02441H108.88C108.453 8.02441 108.168 8.31023 108.168 8.73893V29.3653C108.168 29.6034 108.263 29.9131 108.69 30.1274L111.587 31.3183C112.893 31.89 114.317 32.1758 115.861 32.1758C120.776 32.1758 123.839 28.9841 123.839 23.5775C123.839 18.2185 121.037 14.884 116.549 14.884ZM116.027 18.9092C118.211 18.9092 119.494 20.6479 119.494 23.5775C119.494 26.5786 118.164 28.1267 115.861 28.1267C114.982 28.1267 114.033 27.9362 113.296 27.6027L112.513 27.2455V23.4823C112.513 22.1008 112.869 18.9092 116.027 18.9092Z" fill="#004987"/>
      <path d="M130.629 26.7215C130.629 27.0788 130.771 27.3407 131.057 27.3407C131.128 27.3407 131.199 27.3407 131.246 27.3407C131.508 27.2693 131.697 27.3407 131.816 27.5789L133.17 31.0088C133.265 31.2707 133.194 31.4613 132.98 31.5804C132.386 31.8423 131.65 31.9853 131.057 31.9853C127.519 31.9853 125.382 30.0322 125.382 26.7215V8.45313C125.382 8.19114 125.548 8.02441 125.809 8.02441H130.202C130.463 8.02441 130.629 8.19114 130.629 8.45313V26.7215Z" fill="#004987"/>
      <path d="M145.35 31.6278C144.186 31.8898 143.07 31.9851 142.216 31.9851C136.802 31.9851 133.882 28.6982 133.882 23.5059C133.882 18.2659 136.873 15.0029 142.073 15.0029C146.204 15.0029 148.745 17.1941 148.745 20.6001C148.745 23.2439 146.893 24.7682 143.094 25.2922L139.224 25.8639C139.841 27.031 140.673 27.5073 142.216 27.5073C142.904 27.5073 143.854 27.3644 144.328 27.2691L146.299 26.8404C146.561 26.7689 146.727 26.8642 146.798 27.1501L147.724 30.5798C147.795 30.8895 147.653 31.0323 147.391 31.1038L145.35 31.6278ZM142.31 21.9577C143.331 21.8625 143.83 21.3861 143.83 20.6716C143.83 19.8856 143.165 19.4807 141.859 19.4807C140.435 19.4807 138.987 20.4096 138.891 22.4817L142.31 21.9577Z" fill="#004987"/>
      <path d="M147.154 7.14332C146.822 6.5717 146.182 6.23824 145.493 6.38116L143.973 6.69079L144.282 8.21513C144.377 8.62 144.591 8.97733 144.947 9.19169C145.208 9.35837 145.493 9.45365 145.802 9.45365C145.873 9.45365 145.92 9.45365 145.991 9.42985C145.92 9.50133 145.849 9.57273 145.802 9.64421C145.635 9.90617 145.588 10.192 145.635 10.4778C145.706 10.7637 145.873 11.0256 146.11 11.1924C146.3 11.3115 146.514 11.3829 146.728 11.3829C146.799 11.3829 146.87 11.3829 146.965 11.3591C147.416 11.2638 147.748 10.9066 147.843 10.454C148.057 10.7637 148.39 10.9542 148.769 10.9542C148.84 10.9542 148.911 10.9542 149.007 10.9304C149.291 10.8589 149.553 10.6922 149.719 10.454C149.885 10.192 149.933 9.90617 149.885 9.62041C149.79 9.21549 149.505 8.90585 149.125 8.78677C149.885 8.5724 150.384 7.78641 150.217 7.00042L149.909 5.47607L148.39 5.78571C147.701 5.92861 147.226 6.50024 147.154 7.14332Z" fill="#FF8E1C"/>
      <path d="M161.116 1.33186L160.854 3.68984C160.712 4.976 159.548 5.92872 158.266 5.78581L155.916 5.52382L156.177 3.16584C156.319 1.87967 157.483 0.926956 158.765 1.06986L161.116 1.33186Z" fill="#FF8E1C"/>
      <path d="M159.002 8.14366C158.907 9.04871 158.076 9.71567 157.174 9.62039C156.271 9.52511 155.607 8.69147 155.702 7.7864L155.868 6.11914L157.53 6.28587C158.432 6.40496 159.097 7.21477 159.002 8.14366Z" fill="#FF8E1C"/>
      <path d="M151.167 0.259766L150.905 2.61774C150.763 3.90391 151.713 5.07099 152.996 5.21389L155.345 5.47589L155.607 3.11791C155.75 1.83175 154.799 0.66467 153.517 0.521763L151.167 0.259766Z" fill="#FF8E1C"/>
      <path d="M151.785 7.35736C151.69 8.26243 152.354 9.09606 153.256 9.19134C154.159 9.28662 154.99 8.61974 155.085 7.71462L155.251 6.04737L153.589 5.88065C152.71 5.78537 151.879 6.45228 151.785 7.35736Z" fill="#FF8E1C"/>
      <path d="M137.8 4.92752C137.776 4.6417 137.681 4.35588 137.514 4.11771C137.254 3.73662 136.85 3.47462 136.399 3.37935L134.665 3.0459L134.333 4.7846C134.167 5.7135 134.737 6.61858 135.663 6.83294C135.236 6.97585 134.879 7.33312 134.785 7.80948C134.665 8.5002 135.117 9.16713 135.805 9.28621C135.853 9.28621 135.876 9.31001 135.924 9.31001C136.209 9.33381 136.517 9.26241 136.755 9.09565C136.922 8.97657 137.04 8.83369 137.135 8.69073C137.206 9.21473 137.61 9.64345 138.156 9.73873C138.203 9.73873 138.227 9.76253 138.274 9.76253C138.559 9.78633 138.868 9.71493 139.105 9.54817C139.391 9.35761 139.58 9.07185 139.628 8.73841C139.699 8.40493 139.628 8.07148 139.438 7.78566C139.367 7.66658 139.271 7.59512 139.177 7.49985C139.2 7.49985 139.225 7.49985 139.248 7.49985C139.651 7.54749 140.056 7.42839 140.411 7.19022C140.791 6.92821 141.053 6.52331 141.148 6.07077L141.48 4.33207L139.747 3.99861C138.916 3.85571 138.156 4.26061 137.8 4.92752Z" fill="#004987"/>
      <path d="M151.262 15.3126C151.286 15.003 151.239 14.6934 151.12 14.4075C150.906 13.955 150.55 13.5977 150.075 13.4072L148.318 12.7402L147.654 14.5028C147.297 15.4317 147.748 16.4796 148.651 16.8846C148.176 16.9322 147.725 17.2419 147.534 17.742C147.274 18.4328 147.606 19.2187 148.318 19.4808C148.366 19.5046 148.389 19.5046 148.437 19.5284C148.745 19.6236 149.054 19.5998 149.339 19.4569C149.529 19.3616 149.695 19.2425 149.814 19.0996C149.791 19.6474 150.123 20.1952 150.669 20.4096C150.716 20.4335 150.74 20.4335 150.788 20.4573C151.096 20.5526 151.405 20.5287 151.69 20.3858C152.022 20.2429 152.26 19.9809 152.402 19.6474C152.52 19.314 152.52 18.9329 152.379 18.6233C152.331 18.5042 152.236 18.3851 152.141 18.2898C152.165 18.2898 152.188 18.3137 152.212 18.3137C152.64 18.4328 153.067 18.409 153.471 18.2184C153.922 18.004 154.277 17.6467 154.468 17.1704L155.133 15.4078L153.376 14.741C152.616 14.4075 151.737 14.6695 151.262 15.3126Z" fill="#004987"/>
      <path d="M139.77 12.0971C139.604 11.8351 139.367 11.6207 139.082 11.5016C138.631 11.2873 138.132 11.2397 137.657 11.4064L135.876 12.0018L136.47 13.7882C136.802 14.7408 137.799 15.2648 138.749 14.9791C138.417 15.3363 138.274 15.8603 138.44 16.3366C138.678 17.0512 139.438 17.4323 140.15 17.1703C140.197 17.1465 140.222 17.1465 140.269 17.1227C140.554 17.0036 140.791 16.7654 140.934 16.4796C141.028 16.289 141.076 16.0985 141.076 15.8841C141.408 16.3128 142.002 16.5272 142.572 16.3366C142.619 16.3128 142.643 16.3128 142.691 16.289C142.976 16.17 143.213 15.9318 143.356 15.646C143.522 15.3125 143.545 14.9552 143.427 14.6218C143.308 14.2883 143.071 14.0026 142.739 13.8358C142.619 13.7643 142.477 13.7405 142.334 13.7167C142.359 13.7167 142.382 13.6929 142.406 13.6929C142.809 13.5262 143.118 13.2165 143.331 12.8116C143.545 12.3591 143.593 11.8589 143.427 11.3826L142.833 9.59619L141.053 10.1917C140.222 10.5251 139.723 11.2873 139.77 12.0971Z" fill="#004987"/>
      <path d="M54.7211 17.8374C54.4599 17.8374 54.1513 18.028 53.7951 18.29C52.9166 18.9807 51.9668 19.8619 50.8509 20.9338C50.8509 20.9338 49.735 14.7173 48.5953 14.7173C46.672 14.7173 47.5268 21.4816 45.5799 21.4816C44.5351 21.1958 45.3186 17.6469 43.6804 17.6469C43.3005 17.6469 43.1106 17.9327 43.0156 18.3138C42.3983 18.7663 41.5909 19.4332 40.3088 19.4332C38.3381 19.4332 37.3646 16.0272 36.1063 16.0272C33.8506 16.0272 33.1146 19.0045 31.9036 19.0045C30.8114 19.0045 30.574 15.0745 28.627 15.0745C26.2052 15.0745 26.0627 20.124 25.2792 20.124C25.0418 20.124 24.9705 19.9811 24.9705 19.7428C24.9705 19.5285 25.0418 19.2188 25.0418 18.9093C25.0418 18.5997 24.9705 18.29 24.7568 18.028C24.5194 17.766 24.3057 17.6707 24.1158 17.6707C23.4035 17.6707 22.9761 19.0045 22.9761 19.5762C22.9761 21.1482 23.6409 22.7201 25.3742 22.7201C27.1549 22.7201 27.4636 17.2658 28.4608 17.2658C28.8645 17.2658 29.458 21.1005 31.6899 21.1005C33.5894 21.1005 34.3967 18.028 35.5601 18.028C36.6049 18.028 37.0085 21.0767 40.1189 21.0767C41.2111 21.0767 42.232 20.7195 42.9681 20.2193C43.3005 21.8865 44.4402 23.4585 45.4611 23.4585C48.2154 23.4585 48.2866 18.1947 48.2866 18.1947L49.1651 22.2914C49.1651 22.2914 44.084 26.8406 44.084 35.7486C44.084 38.0827 45.2474 40.1786 47.4319 40.1786C50.8509 40.1786 52.0619 33.1285 52.0619 29.675C52.0619 26.1737 51.2545 22.6248 51.2545 22.6248C52.0619 21.696 52.8454 20.8861 54.1275 20.0287C54.8636 19.5285 55.1485 18.7187 55.1485 18.3376C55.196 18.1709 55.0535 17.8374 54.7211 17.8374ZM47.2894 38.035C46.4346 38.035 45.6986 37.2967 45.6986 35.7248C45.6986 28.2935 49.6637 24.1015 49.6637 24.1015C49.6637 24.1015 50.3998 27.5314 50.3998 30.0084C50.3998 32.414 49.6875 38.035 47.2894 38.035Z" fill="#FF8E1C"/>
      <path d="M67.8275 8.85778C72.22 8.85778 74.8318 11.1443 74.8318 15.0028C74.8318 18.0516 72.9798 20.0046 69.9881 20.9811C69.5845 21.1241 69.5607 21.3146 69.9644 21.5051C71.5789 22.2912 72.6474 23.8631 73.3597 25.578L75.7578 31.2467C75.8528 31.4849 75.7578 31.6039 75.5678 31.6039H73.8583C73.6921 31.6039 73.5971 31.5325 73.5259 31.3658L71.389 26.4116C70.1069 23.2915 68.1598 21.8862 64.8833 21.8862H61.0843V31.3181C61.0843 31.5086 60.9893 31.6277 60.7757 31.6277H59.1849C58.9949 31.6277 58.8762 31.5324 58.8762 31.3181V9.14363C58.8762 8.95306 58.9712 8.83398 59.1849 8.83398H67.8275V8.85778ZM65.8093 19.9093C70.1306 19.9093 72.6 18.1468 72.6 15.1219C72.6 12.4305 70.7954 10.8585 67.8275 10.8585H61.0606V19.9331H65.8093V19.9093Z" fill="#004987"/>
      <path d="M82.2398 15.2651C80.9339 15.2651 79.723 15.4318 78.6545 15.7653L77.4198 16.1226C77.2774 16.1702 77.1824 16.2417 77.1112 16.3607C77.0637 16.4798 77.0637 16.5989 77.0875 16.7419L77.4436 18.0518C77.4911 18.1947 77.5624 18.29 77.681 18.3615C77.7998 18.4091 77.9184 18.4091 78.0609 18.3615L79.2244 18.028C80.0316 17.766 81.0289 17.6469 82.1686 17.6469C84.899 17.6469 86.4186 19.1474 86.4186 21.9103V22.1723H81.6462C77.8947 22.1961 75.6628 24.1016 75.6628 27.2217C75.6628 30.318 77.7047 32.1759 81.1238 32.1759C83.5694 32.1759 85.3264 31.4613 86.4898 29.9608C86.6798 29.7464 86.7748 29.7464 86.7748 29.7464C86.7748 29.7464 86.8223 29.7702 86.846 29.8893L86.9409 31.3898C86.9884 31.6757 87.1784 31.8424 87.4395 31.8424H88.4606C88.7692 31.8424 88.9354 31.6757 88.9354 31.366V21.8627C88.9592 17.7899 86.3712 15.2651 82.2398 15.2651ZM81.2901 29.8179C79.2956 29.8179 78.1796 28.8175 78.1796 27.055C78.1796 25.3163 79.4143 24.3635 81.6699 24.3635H86.4661V24.6256C86.4423 27.9125 84.5666 29.8179 81.2901 29.8179Z" fill="#004987"/>
      <path d="M99.2879 15.0745C97.4122 15.0745 95.9163 15.7414 94.8242 17.0038C94.6817 17.1467 94.5867 17.1467 94.5629 17.1229C94.5392 17.1229 94.5155 17.0276 94.5867 16.8609C95.0378 15.5985 95.1328 14.5981 95.1328 13.0976V8.57222C95.1328 8.23877 94.9191 8.02441 94.5867 8.02441H91.6426C91.3101 8.02441 91.0964 8.23877 91.0964 8.57222V29.5558C91.0964 29.8178 91.2389 30.0084 91.5 30.1512L94.3968 31.3422C95.6789 31.89 97.0797 32.1758 98.5994 32.1758C103.419 32.1758 106.387 28.889 106.387 23.6013C106.411 18.3375 103.68 15.0745 99.2879 15.0745ZM98.7656 18.7425C101.045 18.7425 102.422 20.5526 102.422 23.6013C102.422 26.65 100.997 28.484 98.5994 28.484C97.6971 28.484 96.7236 28.2697 95.9638 27.9362L95.0854 27.5313V23.4823C95.0854 20.4573 96.4387 18.7425 98.7656 18.7425Z" fill="#004987"/>
      <path d="M116.549 14.884C114.697 14.884 113.202 15.5033 112.062 16.7417C112.465 15.5271 112.537 14.5505 112.537 13.0976V8.73893C112.537 8.31023 112.251 8.02441 111.825 8.02441H108.88C108.453 8.02441 108.168 8.31023 108.168 8.73893V29.3653C108.168 29.6034 108.263 29.9131 108.69 30.1274L111.587 31.3183C112.893 31.89 114.317 32.1758 115.861 32.1758C120.776 32.1758 123.839 28.9841 123.839 23.5775C123.839 18.2185 121.037 14.884 116.549 14.884ZM116.027 18.9092C118.211 18.9092 119.494 20.6479 119.494 23.5775C119.494 26.5786 118.164 28.1267 115.861 28.1267C114.982 28.1267 114.033 27.9362 113.296 27.6027L112.513 27.2455V23.4823C112.513 22.1008 112.869 18.9092 116.027 18.9092Z" fill="#004987"/>
      <path d="M130.629 26.7215C130.629 27.0788 130.771 27.3407 131.057 27.3407C131.128 27.3407 131.199 27.3407 131.246 27.3407C131.508 27.2693 131.697 27.3407 131.816 27.5789L133.17 31.0088C133.265 31.2707 133.194 31.4613 132.98 31.5804C132.386 31.8423 131.65 31.9853 131.057 31.9853C127.519 31.9853 125.382 30.0322 125.382 26.7215V8.45313C125.382 8.19114 125.548 8.02441 125.809 8.02441H130.202C130.463 8.02441 130.629 8.19114 130.629 8.45313V26.7215Z" fill="#004987"/>
      <path d="M145.35 31.6278C144.186 31.8898 143.07 31.9851 142.216 31.9851C136.802 31.9851 133.882 28.6982 133.882 23.5059C133.882 18.2659 136.873 15.0029 142.073 15.0029C146.204 15.0029 148.745 17.1941 148.745 20.6001C148.745 23.2439 146.893 24.7682 143.094 25.2922L139.224 25.8639C139.841 27.031 140.673 27.5073 142.216 27.5073C142.904 27.5073 143.854 27.3644 144.328 27.2691L146.299 26.8404C146.561 26.7689 146.727 26.8642 146.798 27.1501L147.724 30.5798C147.795 30.8895 147.653 31.0323 147.391 31.1038L145.35 31.6278ZM142.31 21.9577C143.331 21.8625 143.83 21.3861 143.83 20.6716C143.83 19.8856 143.165 19.4807 141.859 19.4807C140.435 19.4807 138.987 20.4096 138.891 22.4817L142.31 21.9577Z" fill="#004987"/>
      <path d="M147.154 7.14332C146.822 6.5717 146.182 6.23824 145.493 6.38116L143.973 6.69079L144.282 8.21513C144.377 8.62 144.591 8.97733 144.947 9.19169C145.208 9.35837 145.493 9.45365 145.802 9.45365C145.873 9.45365 145.92 9.45365 145.991 9.42985C145.92 9.50133 145.849 9.57273 145.802 9.64421C145.635 9.90617 145.588 10.192 145.635 10.4778C145.706 10.7637 145.873 11.0256 146.11 11.1924C146.3 11.3115 146.514 11.3829 146.728 11.3829C146.799 11.3829 146.87 11.3829 146.965 11.3591C147.416 11.2638 147.748 10.9066 147.843 10.454C148.057 10.7637 148.39 10.9542 148.769 10.9542C148.84 10.9542 148.911 10.9542 149.007 10.9304C149.291 10.8589 149.553 10.6922 149.719 10.454C149.885 10.192 149.933 9.90617 149.885 9.62041C149.79 9.21549 149.505 8.90585 149.125 8.78677C149.885 8.5724 150.384 7.78641 150.217 7.00042L149.909 5.47607L148.39 5.78571C147.701 5.92861 147.226 6.50024 147.154 7.14332Z" fill="#FF8E1C"/>
      <path d="M161.116 1.33186L160.854 3.68984C160.712 4.976 159.548 5.92872 158.266 5.78581L155.916 5.52382L156.177 3.16584C156.319 1.87967 157.483 0.926956 158.765 1.06986L161.116 1.33186Z" fill="#FF8E1C"/>
      <path d="M159.002 8.14366C158.907 9.04871 158.076 9.71567 157.174 9.62039C156.271 9.52511 155.607 8.69147 155.702 7.7864L155.868 6.11914L157.53 6.28587C158.432 6.40496 159.097 7.21477 159.002 8.14366Z" fill="#FF8E1C"/>
      <path d="M151.167 0.259766L150.905 2.61774C150.763 3.90391 151.713 5.07099 152.996 5.21389L155.345 5.47589L155.607 3.11791C155.75 1.83175 154.799 0.66467 153.517 0.521763L151.167 0.259766Z" fill="#FF8E1C"/>
      <path d="M151.785 7.35736C151.69 8.26243 152.354 9.09606 153.256 9.19134C154.159 9.28662 154.99 8.61974 155.085 7.71462L155.251 6.04737L153.589 5.88065C152.71 5.78537 151.879 6.45228 151.785 7.35736Z" fill="#FF8E1C"/>
      <path d="M137.8 4.92752C137.776 4.6417 137.681 4.35588 137.514 4.11771C137.254 3.73662 136.85 3.47462 136.399 3.37935L134.665 3.0459L134.333 4.7846C134.167 5.7135 134.737 6.61858 135.663 6.83294C135.236 6.97585 134.879 7.33312 134.785 7.80948C134.665 8.5002 135.117 9.16713 135.805 9.28621C135.853 9.28621 135.876 9.31001 135.924 9.31001C136.209 9.33381 136.517 9.26241 136.755 9.09565C136.922 8.97657 137.04 8.83369 137.135 8.69073C137.206 9.21473 137.61 9.64345 138.156 9.73873C138.203 9.73873 138.227 9.76253 138.274 9.76253C138.559 9.78633 138.868 9.71493 139.105 9.54817C139.391 9.35761 139.58 9.07185 139.628 8.73841C139.699 8.40493 139.628 8.07148 139.438 7.78566C139.367 7.66658 139.271 7.59512 139.177 7.49985C139.2 7.49985 139.225 7.49985 139.248 7.49985C139.651 7.54749 140.056 7.42839 140.411 7.19022C140.791 6.92821 141.053 6.52331 141.148 6.07077L141.48 4.33207L139.747 3.99861C138.916 3.85571 138.156 4.26061 137.8 4.92752Z" fill="#004987"/>
      <path d="M151.262 15.3126C151.286 15.003 151.239 14.6934 151.12 14.4075C150.906 13.955 150.55 13.5977 150.075 13.4072L148.318 12.7402L147.654 14.5028C147.297 15.4317 147.748 16.4796 148.651 16.8846C148.176 16.9322 147.725 17.2419 147.534 17.742C147.274 18.4328 147.606 19.2187 148.318 19.4808C148.366 19.5046 148.389 19.5046 148.437 19.5284C148.745 19.6236 149.054 19.5998 149.339 19.4569C149.529 19.3616 149.695 19.2425 149.814 19.0996C149.791 19.6474 150.123 20.1952 150.669 20.4096C150.716 20.4335 150.74 20.4335 150.788 20.4573C151.096 20.5526 151.405 20.5287 151.69 20.3858C152.022 20.2429 152.26 19.9809 152.402 19.6474C152.52 19.314 152.52 18.9329 152.379 18.6233C152.331 18.5042 152.236 18.3851 152.141 18.2898C152.165 18.2898 152.188 18.3137 152.212 18.3137C152.64 18.4328 153.067 18.409 153.471 18.2184C153.922 18.004 154.277 17.6467 154.468 17.1704L155.133 15.4078L153.376 14.741C152.616 14.4075 151.737 14.6695 151.262 15.3126Z" fill="#004987"/>
      <path d="M139.77 12.0971C139.604 11.8351 139.367 11.6207 139.082 11.5016C138.631 11.2873 138.132 11.2397 137.657 11.4064L135.876 12.0018L136.47 13.7882C136.802 14.7408 137.799 15.2648 138.749 14.9791C138.417 15.3363 138.274 15.8603 138.44 16.3366C138.678 17.0512 139.438 17.4323 140.15 17.1703C140.197 17.1465 140.222 17.1465 140.269 17.1227C140.554 17.0036 140.791 16.7654 140.934 16.4796C141.028 16.289 141.076 16.0985 141.076 15.8841C141.408 16.3128 142.002 16.5272 142.572 16.3366C142.619 16.3128 142.643 16.3128 142.691 16.289C142.976 16.17 143.213 15.9318 143.356 15.646C143.522 15.3125 143.545 14.9552 143.427 14.6218C143.308 14.2883 143.071 14.0026 142.739 13.8358C142.619 13.7643 142.477 13.7405 142.334 13.7167C142.359 13.7167 142.382 13.6929 142.406 13.6929C142.809 13.5262 143.118 13.2165 143.331 12.8116C143.545 12.3591 143.593 11.8589 143.427 11.3826L142.833 9.59619L141.053 10.1917C140.222 10.5251 139.723 11.2873 139.77 12.0971Z" fill="#004987"/>
      <path d="M155.107 33.7456C154.819 33.7456 154.549 33.6916 154.296 33.5835C154.045 33.4755 153.822 33.3258 153.63 33.1348C153.439 32.9423 153.289 32.7202 153.179 32.4685C153.071 32.2168 153.017 31.9466 153.019 31.658C153.021 31.3694 153.075 31.0991 153.184 30.8475C153.293 30.5957 153.442 30.3743 153.634 30.1832C153.825 29.9908 154.046 29.8405 154.298 29.7324C154.55 29.6243 154.819 29.5703 155.107 29.5703C155.396 29.5703 155.665 29.6243 155.918 29.7324C156.171 29.8405 156.392 29.9908 156.581 30.1832C156.773 30.3743 156.922 30.5957 157.03 30.8475C157.138 31.0991 157.193 31.3694 157.194 31.658C157.196 31.9466 157.143 32.2168 157.034 32.4685C156.926 32.7202 156.777 32.9423 156.586 33.1348C156.394 33.3258 156.172 33.4755 155.919 33.5835C155.666 33.6916 155.396 33.7456 155.107 33.7456ZM155.107 33.287C155.332 33.287 155.543 33.2448 155.741 33.1604C155.939 33.0761 156.112 32.9595 156.262 32.8105C156.41 32.6616 156.527 32.4889 156.612 32.2925C156.696 32.0949 156.737 31.884 156.736 31.66C156.734 31.4346 156.692 31.2231 156.607 31.0253C156.523 30.8277 156.406 30.6543 156.257 30.5054C156.108 30.3564 155.936 30.2399 155.74 30.1555C155.543 30.0711 155.332 30.0289 155.107 30.0289C154.883 30.0289 154.672 30.0711 154.476 30.1555C154.28 30.2399 154.107 30.3571 153.959 30.5074C153.809 30.6563 153.692 30.8296 153.606 31.0273C153.522 31.2237 153.479 31.4346 153.478 31.66C153.477 31.8827 153.518 32.0929 153.602 32.2906C153.686 32.4869 153.803 32.6596 153.952 32.8085C154.102 32.9575 154.276 33.0748 154.472 33.1604C154.67 33.2448 154.881 33.287 155.107 33.287Z" fill="#004987"/>
      <path d="M154.104 32.8269V30.4888H155.084C155.286 30.4888 155.461 30.5233 155.608 30.5923C155.754 30.6591 155.867 30.756 155.947 30.8829C156.027 31.0098 156.067 31.1612 156.067 31.3372C156.067 31.5109 156.027 31.6612 155.947 31.7881C155.867 31.9128 155.754 32.0086 155.608 32.0754C155.461 32.1422 155.286 32.1756 155.084 32.1756H154.395L154.628 31.9384V32.8269H154.104ZM155.543 32.8269L154.977 31.9785H155.537L156.109 32.8269H155.543ZM154.628 31.9986L154.395 31.7447H155.055C155.217 31.7447 155.337 31.709 155.417 31.6378C155.497 31.5643 155.537 31.4641 155.537 31.3372C155.537 31.208 155.497 31.1078 155.417 31.0366C155.337 30.9653 155.217 30.9297 155.055 30.9297H154.395L154.628 30.6725V31.9986Z" fill="#004987"/>
    </g>
  </g>
  <defs>
    <clipPath id="clip0_8379_20025">
      <rect width="178" height="40" fill="white" transform="translate(-4 0.259766)"/>
    </clipPath>
  </defs>
</svg>`;

const ManageProgressAndCalendarCard = ({
  selectedTopic,
}: {
  selectedTopic: any;
}) => {
  const { persistedUser } = usePersistedUser();
  const { data: userData } = persistedUser;
  const userFirstName = userData?.firstname || "User";
  const userDailyTrackersKey = {
    startDate: startOfDay(subDays(new Date(), 90)),
    endDate: endOfDay(new Date()), // End of today
    topicId: selectedTopic?._id?.toString(),
  };
  const last90Days = subDays(new Date(), 90);
  const {
    data: userDailyTrackers,
    isLoading: trackersLoading,
    refetch: refetchTrackers,
  } = trpc.manage.getUserDailyTrackers.useQuery(userDailyTrackersKey, {
    enabled: !!selectedTopic?._id,
  });

  const fetchReport = trpc.manage.getProgressReport.useMutation();

  const isGoodDay = (dailyTracker: any) => {
    return dailyTracker.answers.some(
      (answer: any) => answer?.rawOptionData?.color === "#25BA60"
    );
  };

  const isBadDay = (dailyTracker: any) => {
    return dailyTracker.answers.some(
      (answer: any) =>
        answer?.rawOptionData?.color === "#FFBE1C" ||
        answer?.rawOptionData?.color === "#E54D4D"
    );
  };

  // Filtering good and bad days from all user daily trackers
  const goodDays = userDailyTrackers?.filter(isGoodDay).length || 0;
  const difficultyDays = userDailyTrackers?.filter(isBadDay).length || 0;

  console.log({ difficultyDays, goodDays, name: userData?.firstname });
  const generateMyProgressReportPdf = async () => {
    try {
      const report = await fetchReport.mutateAsync({});
      console.log({ report });
      // Q2: Asthma Control and My Confidence - Mixed Chart (Bar + Line)
      const asthmaControlAndConfidenceChart = encodeURIComponent(`{
        type: 'bar',
        data: {
          labels: ['January', 'February', 'March', 'April', 'May', 'June'],
          datasets: [
            {
              label: 'Asthma Control',
              data: [1, 4, 3, 5, 3, 5],
              backgroundColor: '#0B79D3',
              yAxisID: 'y'
            },
            {
              label: 'Confidence',
              data: [2, 4, 3, 3, 4, 4],
              backgroundColor: '#FF8E1C',
              yAxisID: 'y'
            },
            {
              type: 'line',
              label: 'Linear (Asthma Control)',
              data: [2, 3, 3.5, 4, 4.2, 4.5],
              borderColor: '#0B79D3',
              borderDash: [5, 5],
              fill: false,
              yAxisID: 'y1'
            },
            {
              type: 'line',
              label: 'Linear (Confidence)',
              data: [1.5, 2.5, 3, 3.2, 3.5, 3.8],
              borderColor: '#FF8E1C',
              borderDash: [5, 5],
              fill: false,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          scales: {
            y: {
              beginAtZero: true,
              max: 6,
              position: 'left'
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              beginAtZero: true,
              max: 6,
              grid: {
                drawOnChartArea: false,
              }
            }
          },
          legend: { display: true, position: 'bottom' }
        }
      }`);

      // Q3: Asthma Control - Bar Chart
      const asthmaControlChart = encodeURIComponent(`{
        type: 'bar',
        data: {
          labels: ['March', 'April', 'May', 'June'],
          datasets: [
            { label: 'Unplanned Visits', data: [4, 3, 4, 5], backgroundColor: '#0B79D3' },
            { label: 'Admissions', data: [2, 5, 1, 2], backgroundColor: '#FF8E1C' },
            { label: 'Overnight Stays', data: [1, 0, 3, 1], backgroundColor: '#9B59B6' },
            { label: 'Oral Steroid Use', data: [3, 3, 3, 4], backgroundColor: '#1ABC9C' },
            { label: 'Missed Activity', data: [3, 4, 5, 3], backgroundColor: '#9B59B6' }
          ]
        },
        options: {
          scales: { y: { beginAtZero: true, max: 6 } },
          legend: { display: true, position: 'bottom' }
        }
      }`);

      const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>MyProgress Report</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 16px;
              color: #000;
            }
            h2 {
              margin-bottom: 8px;
              color: #004987;
            }
            .container {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
            }
            .left-col, .right-col {
              width: 48%;
            }
            .chart-container {
              margin-bottom: 32px;
              border: 1px solid #ccc;
              padding: 12px;
            }
            .discussion {
              font-size: 13px;
              margin-bottom: 16px;
            }
            .discussion strong {
              background-color: yellow;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 8px;
            }
            td, th {
              border: 1px solid #000;
              padding: 6px 10px;
              font-size: 13px;
              text-align: left;
            }
            th {
              background-color: #0B79D3;
              color: white;
              font-weight: bold;
            }
            .section-title {
              font-size: 14px;
              font-weight: bold;
              margin: 20px 0 10px 0;
              color: #004987;
            }
          </style>
        </head>
        <body>
           <div class="container">
            <div class="left-col">
              <div class="chart-container">
                <h3>Asthma Control and My Confidence</h3>
                <img src="https://quickchart.io/chart?c=${asthmaControlAndConfidenceChart}" width="100%" />
              </div>
            </div>

            <div class="right-col">
              <div class="chart-container">
                <h3>Asthma Control</h3>
                <img src="https://quickchart.io/chart?c=${asthmaControlChart}" width="100%" />
              </div>
            </div>
          </div>

          <div class="section-title">Shared Decision-Making</div>
          <ul style="margin-left: 20px;">
            <li>Action Plan, etc.</li>
            <li>Ikitlik</li>
            <li>Jtkktlu</li>
          </ul>

          <div class="section-title">Impact and Risk (rolling 365)</div>
          <table>
            <tr>
              <th>Measurement</th>
              <th>Count</th>
              <th>Measurement</th>
              <th>Count</th>
            </tr>
            <tr>
              <td>Oral Steroid Use</td>
              <td></td>
              <td>Overnight Hospital Stay</td>
              <td></td>
            </tr>
            <tr>
              <td>Unplanned Medical or ER Visit</td>
              <td></td>
              <td>Missed School or Work</td>
              <td></td>
            </tr>
            <tr>
              <td>Hospital Admission</td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </table>
        </body>
      </html>




                  <li>Action plan: create/review (create if “no”)</li>
                  <li>Severity (if “no” or “I don’t know”)</li>







      `;

      const options = {
        // html: htmlContent,
        fileName: "MyProgress_Report",
        directory: "Documents",
      };

      const { uri } = await Print.printToFileAsync({ html: report });

      // Suggest a filename with date and topic name
      const currentDate = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format
      const topicName =
        selectedTopic?.name?.replace(/[^a-zA-Z0-9]/g, "_") || "Progress"; // Clean topic name for filename
      const fileName = `${topicName}_Report_${currentDate}.pdf`;
      const newUri = FileSystem.documentDirectory + fileName;

      // Move the PDF to a permanent location
      await FileSystem.moveAsync({ from: uri, to: newUri });

      await Sharing.shareAsync(newUri, {
        mimeType: "application/pdf",
        UTI: "com.adobe.pdf",
      });

      Toast.show({
        type: "success",
        text1: `Your report is saved as: ${fileName}`,
        visibilityTime: 5000,
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: `Failed to generate report`,
        visibilityTime: 5000,
      });
      console.error(error);
    }
  };

  return (
    <View className="flex-row space-x-2 px-1 py-2 mr-1">
      {/* myProgress Card */}
      <View
        className="w-[50%] p-4 bg-white rounded-xl shadow-md border border-gray-200"
        style={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 8,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 8,
              }
        }
      >
        <Pressable
          onPress={() => {
            console.log("DEBUG: myProgress card clicked ***");
            generateMyProgressReportPdf();
          }}
        >
          <View className="flex-row items-center w-[75%]">
            <MyProgressIcon />
            <Text
              adjustsFontSizeToFit
              className="ml-2 text-[#336D9F] text-center font-montserrat text-[14px] font-semibold leading-[130%]"
            >
              myProgress
            </Text>
          </View>
          <Text className="m-auto mt-[-8px] text-[#336D9F] font-montserrat text-[10px] font-normal leading-[130%]"></Text>
        </Pressable>
        <View className="flex-row items-center mt-4">
          <ProgressCircle />
          <Text className=" ml-2 w-[107px] text-[#004987] font-montserrat text-[10px] font-normal leading-[120%]">
            Stay on track! Let’s complete your goals
          </Text>
        </View>
        <View className="flex-row justify-between mt-4">
          <View className="items-center w-[40%]" style={{ gap: 12 }}>
            <ActionPlanIcon />
            <Text className="text-[#004987] text-center font-montserrat text-[10px] font-normal leading-[120%]">
              action plan
            </Text>
          </View>
          <View
            style={{
              width: 1,
              height: 60,
              backgroundColor: "#B4DDFF",
              marginHorizontal: 14,
            }}
          />
          <View className="items-center w-[50%] mt-2" style={{ gap: 12 }}>
            <ActivityTrends />
            <Text className="text-[#004987] text-center font-montserrat text-[10px] font-normal leading-[120%]">
              activity trends
            </Text>
          </View>
        </View>
      </View>

      {/* myCalendar Card */}
      <View
        className="w-[50%] p-4 bg-white rounded-xl shadow-md border border-gray-200"
        style={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 8,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 8,
              }
        }
      >
        <View className="flex-row items-center w-[75%]">
          <CalenderMarkIcon />
          <Text className="ml-2 text-[#336D9F] text-center font-montserrat text-[14px] font-semibold leading-[130%]">
            myCalendar
          </Text>
        </View>
        <View className="flex flex-row mt-6 w-[75%]">
          <NumberIcon className="ml-2" />
          <Text className="ml-4 text-[16px] leading-[150%] font-[400] font-montserrat text-[#004987]">
            Upcoming
          </Text>
        </View>
        <View className="mt-6">
          <Text className="mt-4 text-[13px] leading-[150%] font-[700] font-montserrat text-[#004987]">
            Sept 27th | 3:30
          </Text>
          <Text className="mt-4 text-[12px] leading-[150%] font-[400] font-montserrat text-[#004987]">
            Meeting with Dr. Baker
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ManageProgressAndCalendarCard;
